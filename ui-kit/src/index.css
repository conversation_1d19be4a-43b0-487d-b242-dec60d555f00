@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Color Variables */
:root {
  --white: #fff;
  --orange: oklch(0.6433 0.2133 36.88);
  --red: oklch(0.577 0.245 27.325);
  --teal: oklch(0.6 0.1 185);
  --cyan: oklch(0.4 0.07 227);
  --lime: oklch(0.65 0.18 132);
  --amber: oklch(0.77 0.16 70);

  /* Warm Gray colors */
  --warmgray-50: oklch(0.9848 0.0013 106.42); /*used*/
  --warmgray-100: oklch(0.9699 0.0013 106.42);
  --warmgray-200: oklch(0.9232 0.0026 48.72);
  --warmgray-300: oklch(0.8687 0.0043 56.37);
  --warmgray-400: oklch(0.7161 0.0091 56.26);
  --warmgray-500: oklch(0.5534 0.0116 58.07);
  --warmgray-600: oklch(0.4444 0.0096 73.64);
  --warmgray-700: oklch(0.3741 0.0087 67.56);
  --warmgray-800: oklch(0.2685 0.0063 34.3);
  --warmgray-900: oklch(0.2161 0.0061 56.04); /*used*/
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.5rem;
  --background: var(--warmgray-50);
  --foreground: var(--warmgray-900);
  --card: var(--white);
  --card-foreground: var(--warmgray-900);
  --popover: var(--white);
  --popover-foreground: var(--warmgray-900);
  --primary: var(--orange);
  --primary-foreground: var(--white);
  --secondary: var(--warmgray-200);
  --secondary-foreground: var(--warmgray-900);
  --muted: var(--warmgray-100);
  --muted-foreground: var(--warmgray-500);
  --accent: var(--warmgray-100);
  --accent-foreground: var(--warmgray-900);
  --destructive: var(--red);
  --border: var(--warmgray-300);
  --input: var(--warmgray-200);
  --ring: var(--orange);
  --chart-1: var(--orange);
  --chart-2: var(--teal);
  --chart-3: var(--cyan);
  --chart-4: var(--lime);
  --chart-5: var(--amber);
  --sidebar: var(--warmgray-200);
  --sidebar-foreground: var(--warmgray-900);
  --sidebar-primary: var(--warmgray-300);
  --sidebar-primary-foreground: var(--white);
  --sidebar-accent: var(--warmgray-100);
  --sidebar-accent-foreground: var(--warmgray-900);
  --sidebar-border: var(--warmgray-200);
  --sidebar-ring: var(--orange);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
